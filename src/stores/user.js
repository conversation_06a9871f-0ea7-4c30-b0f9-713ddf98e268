import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import {
  login,
  getUserProfile,
  updateUserProfile,
  updateUserPassword,
  updateUserMobile,
} from '@/api/user'
import router from '@/router'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 设置 token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info && Object.keys(info).length > 0) {
      localStorage.setItem('userInfo', JSON.stringify(info))
    } else {
      localStorage.removeItem('userInfo')
    }
  }

  // 登录
  const userLogin = async (loginForm) => {
    try {
      const response = await login(loginForm)
      const { data } = response

      // 设置 token
      setToken(data.accessToken)

      // 设置用户信息
      setUserInfo(data.userInfo || {})

      return Promise.resolve(data)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // 重置状态（用于 token 过期等情况）
  const resetState = () => {
    setToken('')
    setUserInfo({})
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const response = await getUserProfile()
      const { data } = response
      setUserInfo(data)
      return Promise.resolve(data)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      ElMessage.error('获取用户信息失败')
      return Promise.reject(error)
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData) => {
    try {
      await updateUserProfile(profileData)
      // 更新成功后重新获取用户信息
      await fetchUserProfile()
      ElMessage.success('用户信息更新成功')
      return Promise.resolve()
    } catch (error) {
      console.error('更新用户信息失败:', error)
      ElMessage.error('更新用户信息失败')
      return Promise.reject(error)
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      await updateUserPassword(passwordData)
      ElMessage.success('密码修改成功')
      return Promise.resolve()
    } catch (error) {
      console.error('修改密码失败:', error)
      ElMessage.error('修改密码失败')
      return Promise.reject(error)
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 这里可以调用登出接口
      // await logoutApi()

      resetState()
      router.push('/login')
      return Promise.resolve()
    } catch (error) {
      // 即使接口调用失败，也要清除本地状态
      resetState()
      router.push('/login')
      return Promise.reject(error)
    }
  }

  return {
    // 状态
    token,
    userInfo,
    // 计算属性
    isLoggedIn,
    // 方法
    setToken,
    setUserInfo,
    userLogin,
    fetchUserProfile,
    updateProfile,
    changePassword,
    logout,
    resetState,
  }
})
