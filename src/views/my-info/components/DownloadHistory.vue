<script setup>
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'

const downloadItems = ref([
  {
    id: 1,
    icon: '/组 37.png',
    filename: '20250701XX法院诉讼文件.pdf',
    size: '3.2 MB',
    status: '已完成',
    timestamp: '2025-07-18 18:01:26',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 2,
    icon: '/组 37.png',
    filename: '20250702XX法院诉讼文件.docx',
    size: '1.5 MB',
    status: '已完成',
    timestamp: '2025-07-18 17:30:15',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 3,
    icon: '/组 37.png',
    filename: '20250703XX法院诉讼文件.pdf',
    size: '5.1 MB',
    status: '已完成',
    timestamp: '2025-07-17 16:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 4,
    icon: '/组 37.png',
    filename: '20250704XX法院诉讼文件.docx',
    size: '2.2 MB',
    status: '已完成',
    timestamp: '2025-07-16 15:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 5,
    icon: '/组 37.png',
    filename: '20250705XX法院诉讼文件.pdf',
    size: '4.0 MB',
    status: '已完成',
    timestamp: '2025-07-15 14:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 6,
    icon: '/组 37.png',
    filename: '20250706XX法院诉讼文件.docx',
    size: '1.8 MB',
    status: '已完成',
    timestamp: '2025-07-14 13:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 7,
    icon: '/组 37.png',
    filename: '20250707XX法院诉讼文件.pdf',
    size: '3.5 MB',
    status: '已完成',
    timestamp: '2025-07-13 12:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 8,
    icon: '/组 37.png',
    filename: '20250708XX法院诉讼文件.docx',
    size: '2.0 MB',
    status: '已完成',
    timestamp: '2025-07-12 11:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 9,
    icon: '/组 37.png',
    filename: '20250709XX法院诉讼文件.pdf',
    size: '4.8 MB',
    status: '已完成',
    timestamp: '2025-07-11 10:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 10,
    icon: '/组 37.png',
    filename: '20250710XX法院诉讼文件.docx',
    size: '1.2 MB',
    status: '已完成',
    timestamp: '2025-07-10 09:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 11,
    icon: '/组 37.png',
    filename: '20250711XX法院诉讼文件.pdf',
    size: '3.3 MB',
    status: '已完成',
    timestamp: '2025-07-09 08:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 12,
    icon: '/组 37.png',
    filename: '20250712XX法院诉讼文件.docx',
    size: '1.8 MB',
    status: '已完成',
    timestamp: '2025-07-14 13:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 13,
    icon: '/组 37.png',
    filename: '20250713XX法院诉讼文件.pdf',
    size: '3.5 MB',
    status: '已完成',
    timestamp: '2025-07-13 12:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 14,
    icon: '/组 37.png',
    filename: '20250714XX法院诉讼文件.docx',
    size: '2.0 MB',
    status: '已完成',
    timestamp: '2025-07-12 11:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 15,
    icon: '/组 37.png',
    filename: '20250715XX法院诉讼文件.pdf',
    size: '4.8 MB',
    status: '已完成',
    timestamp: '2025-07-11 10:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 16,
    icon: '/组 37.png',
    filename: '20250716XX法院诉讼文件.docx',
    size: '1.2 MB',
    status: '已完成',
    timestamp: '2025-07-10 09:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 17,
    icon: '/组 37.png',
    filename: '20250717XX法院诉讼文件.pdf',
    size: '3.3 MB',
    status: '已完成',
    timestamp: '2025-07-09 08:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 18,
    icon: '/组 37.png',
    filename: '20250718XX法院诉讼文件.pdf',
    size: '3.2 MB',
    status: '已完成',
    timestamp: '2025-07-18 18:01:26',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 19,
    icon: '/组 37.png',
    filename: '20250719XX法院诉讼文件.docx',
    size: '1.5 MB',
    status: '已完成',
    timestamp: '2025-07-18 17:30:15',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 20,
    icon: '/组 37.png',
    filename: '20250720XX法院诉讼文件.pdf',
    size: '5.1 MB',
    status: '已完成',
    timestamp: '2025-07-17 16:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
  {
    id: 21,
    icon: '/组 37.png',
    filename: '20250721XX法院诉讼文件.docx',
    size: '2.2 MB',
    status: '已完成',
    timestamp: '2025-07-16 15:00:00',
    downloader: '阿琦Aqi',
    downloaderId: '494880',
  },
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => downloadItems.value.length)

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return downloadItems.value.slice(start, end)
})

function handleSizeChange(val) {
  pageSize.value = val
}

function handleCurrentChange(page) {
  currentPage.value = page
}
</script>

<template>
  <div class="download-history-container">
    <div class="table-wrapper">
      <el-table
        :data="paginatedData"
        style="width: 100%"
        height="100%"
        header-row-class-name="table-header-row"
      >
        <el-table-column label="报告信息" min-width="300">
          <template #default="{ row }">
            <div class="report-info">
              <div class="file-icon-wrapper">
                <img class="file-icon" :src="row.icon" />
              </div>
              <div class="file-details">
                <span class="filename">{{ row.filename }}</span>
                <span class="filesize">{{ row.size }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下载状态/时间" min-width="200">
          <template #default="{ row }">
            <div class="download-status">
              <span class="status">{{ row.status }}</span>
              <span class="timestamp">{{ row.timestamp }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下载人" min-width="200">
          <template #default="{ row }">
            <div class="downloader-info">
              <span class="downloader-name">{{ row.downloader }}</span>
              <span class="downloader-id">用户ID: {{ row.downloaderId }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100">
          <template #default>
            <button class="redownload-button">再次下载</button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next"
        :total="total"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.download-history-container {
  font-family: 'HarmonyOS Sans', sans-serif;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #ebebeb;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  width: 26px;
  height: 30px;
}

.file-details,
.download-status,
.downloader-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filename {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
}

.filesize,
.timestamp,
.downloader-id {
  font-size: 14px;
  color: #7f8792;
}

.status,
.downloader-name {
  font-size: 14px;
  color: #222529;
}

.redownload-button {
  font-size: 14px;
  color: #0057d9;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.table-wrapper {
  flex: 1;
  min-height: 0;
}

.pagination-container {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
/* Global style for header */
.el-table .table-header-row th {
  background-color: #f7f9fa !important;
  color: #595e66;
  font-weight: 400;
  border-radius: 0 !important;
}

.el-table .table-header-row th:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.el-table .table-header-row th:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

.el-table .el-table__header-wrapper {
  border-radius: 8px;
}

.el-table,
.el-table__expanded-cell {
  background-color: transparent;
}

.el-table th,
.el-table tr {
  background-color: transparent;
}

.el-table td,
.el-table th.is-leaf {
  border-bottom: 1px solid #f0f1f2;
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
  content: none;
}

.el-pagination.is-background .el-pager li.is-active {
  background-color: #2457d9 !important;
}
</style>
