<script setup>
import { ref, computed } from 'vue'
import OrderInfo from './components/OrderInfo.vue'
import DownloadHistory from './components/DownloadHistory.vue'
import UserProfile from './components/UserProfile.vue'

const tabs = {
  OrderInfo,
  DownloadHistory,
  UserProfile,
}

const activeTab = ref('OrderInfo')

const activeComponent = computed(() => tabs[activeTab.value])

function selectTab(tabName) {
  activeTab.value = tabName
}
</script>

<script>
export default {
  name: 'my-info',
}
</script>
<template>
  <div class="my-info-container">
    <div class="tabs">
      <button
        :class="['tab-button', { active: activeTab === 'OrderInfo' }]"
        @click="selectTab('OrderInfo')"
      >
        订单信息
      </button>
      <button
        :class="['tab-button', { active: activeTab === 'DownloadHistory' }]"
        @click="selectTab('DownloadHistory')"
      >
        下载记录
      </button>
      <button
        :class="['tab-button', { active: activeTab === 'UserProfile' }]"
        @click="selectTab('UserProfile')"
      >
        个人资料
      </button>
    </div>
    <div class="tab-content">
      <keep-alive>
        <component :is="activeComponent" />
      </keep-alive>
    </div>
  </div>
</template>

<style scoped>
.my-info-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
}

.tabs {
  display: flex;
  gap: 32px;
  border-bottom: 1px solid #f0f1f2;
  flex-shrink: 0; /* 防止标签栏被压缩 */
}

.tab-button {
  font-size: 16px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #595e66;
  background: none;
  border: none;
  padding-bottom: 16px;
  cursor: pointer;
  position: relative;
}

.tab-button.active {
  color: #0057d9;
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #0057d9;
}

.tab-content {
  padding-top: 25px;
  flex-grow: 1;
  min-height: 0; /* 确保 flex 子元素可以收缩 */
  overflow: hidden; /* 防止内容溢出 */
}
</style>
