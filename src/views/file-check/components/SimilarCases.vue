<script setup>
defineProps({
  cases: {
    type: Array,
    default: () => [],
  },
});
</script>

<template>
  <div class="similar-cases">
    <h4 class="title">相似案例推荐：</h4>
    <div class="case-list">
      <a href="#" v-for="(caseFile, index) in cases" :key="index" class="case-item">
        <img src="@/assets/icons/pdf.svg" alt="PDF Icon" />
        <span>{{ caseFile.name }}</span>
      </a>
    </div>
  </div>
</template>

<style scoped>
.title {
  font-size: 14px;
  color: #222529;
  margin: 0 0 8px 0;
}

.case-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.case-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  font-size: 12px;
  color: #222529;
  text-decoration: none;
  transition: background-color 0.3s;
}

.case-item:hover {
  background-color: #f0f1f2;
}

.case-item img {
  width: 14px;
  height: 14px;
}
</style>
