<script setup>
import { ref, computed, inject } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import UploadProgress from './UploadProgress.vue'
import {
  uploadFile,
  createConversation,
  createTask,
  submitTask,
  updateConversation,
  getMyConversationList,
  getTaskList,
} from '@/api/checkTask'

const route = useRoute()

// 获取父组件的刷新方法
const refreshConversations = inject('refreshConversations', () => {})

// 根据查询参数获取检查类型和标题
const checkType = computed(() => route.query.type || '0')
const checkTitle = computed(() => {
  const titles = ['判决书检查', '处罚决定书检查', '租赁权拍卖公告检查', '合同检查']
  const index = parseInt(checkType.value) || 0
  return titles[index] || '文书检查'
})

// 当前文书类型（API格式）
const currentType = computed(() => {
  const type = route.query.type || '0'
  return String(parseInt(type) + 1) // URL中0-3对应API中1-4
})

const tasks = ref([]) // 任务列表
const selectedConversationId = ref(null) // 选中的对话ID
const loadingTasks = ref(false)

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取或创建对话
const ensureConversation = async () => {
  try {
    // 获取当前类型的对话列表
    const response = await getMyConversationList(currentType.value)
    const conversations = response.data

    // 查找标题为"新对话"的空对话
    const emptyConversation = conversations.find((conv) => conv.title === '新对话')

    if (emptyConversation) {
      return emptyConversation.id
    } else {
      // 创建新对话
      const createResponse = await createConversation(parseInt(currentType.value))
      return createResponse.data
    }
  } catch (error) {
    console.error('获取或创建对话失败:', error)
    throw error
  }
}

// 更新对话标题
const updateConversationTitle = async (conversationId, fileName) => {
  try {
    // 去掉文件扩展名作为对话标题
    const title = fileName.replace(/\.[^/.]+$/, '')
    await updateConversation({
      id: conversationId,
      title,
    })
  } catch (error) {
    console.error('更新对话标题失败:', error)
    // 不抛出错误，因为这不是关键操作
  }
}

// 获取任务列表
const fetchTasks = async (conversationId) => {
  if (!conversationId) {
    tasks.value = []
    return
  }

  try {
    loadingTasks.value = true
    const response = await getTaskList({ conversationId })
    tasks.value = response.data.map((task) => ({
      id: task.id,
      fileName: getFileNameFromUrl(task.fileUrl),
      fileSize: '', // API没有返回文件大小
      progress: getProgressByStatus(task.status),
      status: getStatusText(task.status),
      taskId: task.id,
      createTime: task.createTime,
      resultFileUrl: task.resultFileUrl,
    }))
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loadingTasks.value = false
  }
}

// 从文件URL中提取文件名
const getFileNameFromUrl = (url) => {
  if (!url) return '未知文件'
  const parts = url.split('/')
  return parts[parts.length - 1]
}

// 根据状态获取进度
const getProgressByStatus = (status) => {
  switch (status) {
    case 0:
      return 0 // 待提交
    case 10:
      return 25 // 待审核
    case 20:
      return 50 // 分析中
    case 30:
      return 100 // 检测完成
    case 40:
      return 100 // 驳回
    default:
      return 0
  }
}

// 根据状态获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0:
      return 'pending' // 待提交
    case 10:
      return 'reviewing' // 待审核
    case 20:
      return 'analyzing' // 分析中
    case 30:
      return 'completed' // 检测完成
    case 40:
      return 'rejected' // 驳回
    default:
      return 'unknown'
  }
}

// 选择对话
const selectConversation = (conversationId) => {
  selectedConversationId.value = conversationId
  fetchTasks(conversationId)
}

// 暴露方法给父组件
defineExpose({
  selectConversation,
})

// 处理文件上传
const handleFileUpload = async (file) => {
  // 验证文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ]
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 PDF、Word、TXT 格式的文件')
    return
  }

  // 验证文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    return
  }

  const fileId = Date.now() + Math.random()
  const taskItem = {
    id: fileId,
    fileName: file.name,
    fileSize: formatFileSize(file.size),
    progress: 0,
    status: 'uploading',
    file: file,
  }

  // 添加到任务列表中
  tasks.value.push(taskItem)

  try {
    // 1. 上传文件
    taskItem.progress = 25
    const uploadResponse = await uploadFile(file)
    const fileUrl = uploadResponse.data

    // 更新进度
    taskItem.progress = 50
    taskItem.status = 'creating'

    // 2. 获取或创建对话
    let conversationId = selectedConversationId.value
    if (!conversationId) {
      conversationId = await ensureConversation()
      selectedConversationId.value = conversationId
    }

    // 3. 创建任务
    const taskResponse = await createTask({
      conversationId,
      fileUrl,
    })
    const taskId = taskResponse.data

    // 更新进度
    taskItem.progress = 75
    taskItem.status = 'submitting'

    // 4. 提交任务
    await submitTask(taskId)

    // 5. 更新对话标题（如果是第一个文件）
    await updateConversationTitle(conversationId, file.name)

    // 完成
    taskItem.progress = 100
    taskItem.status = 'completed'
    taskItem.taskId = taskId

    ElMessage.success('文件上传成功')

    // 刷新历史记录
    refreshConversations()
  } catch (error) {
    console.error('文件上传失败:', error)
    taskItem.status = 'error'
    taskItem.error = error.message || '上传失败'
    ElMessage.error(error.message || '文件上传失败')
  }
}

// 处理文件选择
const handleFileSelect = (event) => {
  const selectedFiles = Array.from(event.target.files)
  selectedFiles.forEach((file) => {
    handleFileUpload(file)
  })
  // 清空input值，允许重复选择同一文件
  event.target.value = ''
}

// 处理拖拽上传
const handleDrop = (event) => {
  event.preventDefault()
  const droppedFiles = Array.from(event.dataTransfer.files)
  droppedFiles.forEach((file) => {
    handleFileUpload(file)
  })
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDragEnter = (event) => {
  event.preventDefault()
}

const handleDragLeave = (event) => {
  event.preventDefault()
}

// 触发文件选择
const triggerFileSelect = () => {
  document.getElementById('file-input').click()
}
</script>

<template>
  <div class="file-upload-container">
    <h1 class="title fixed-header">{{ checkTitle }}</h1>
    <div
      class="upload-box fixed-header"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileSelect"
    >
      <img class="upload-icon" src="@/assets/icons/upload-icon.png" alt="Upload Icon" />
      <div class="upload-text">
        <p>
          <span>将文件拖到此处，或 </span>
          <a class="upload-link">点击上传</a>
        </p>
        <p class="supported-formats">支持 doc、docx、pdf、txt 格式</p>
      </div>
      <input
        id="file-input"
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.txt"
        style="display: none"
        @change="handleFileSelect"
      />
    </div>

    <div class="upload-list scroll-container">
      <!-- 空状态/加载状态容器 -->
      <div v-if="loadingTasks || tasks.length === 0" class="empty-state-container">
        <div v-if="loadingTasks" class="loading-state">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载任务列表...</span>
        </div>
        <div v-else class="empty-state">
          <p>{{ selectedConversationId ? '该对话暂无任务' : '请选择对话查看任务列表' }}</p>
        </div>
      </div>

      <!-- 任务列表 -->
      <UploadProgress
        v-for="task in tasks"
        :key="task.id"
        :file-name="task.fileName"
        :file-size="task.fileSize"
        :progress="task.progress"
        :status="task.status"
        :error="task.error"
        :task-id="task.taskId"
        :result-file-url="task.resultFileUrl"
      />
    </div>
  </div>
</template>

<style scoped>
.file-upload-container {
  flex-grow: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.title {
  font-size: 16px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 500;
  color: #222529;
  margin: 0 0 20px 0;
}

.upload-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 194px;
  border: 1px dashed #bfccdb;
  border-radius: 4px;
  background-color: #ffffff;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-box:hover {
  border-color: #0057d9;
  background-color: #f8f9fa;
}

.upload-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.upload-box:hover .upload-icon {
  transform: scale(1.05);
}

.upload-text p {
  margin: 0;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  line-height: 1.5;
}

.upload-link {
  color: #0057d9;
  font-weight: 500;
  text-decoration: none;
  transition: text-decoration 0.3s ease;
}

.upload-link:hover {
  text-decoration: underline;
}

.supported-formats {
  color: #7f8792;
  margin-top: 16px !important;
}

.upload-list {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-grow: 1;
  overflow-y: auto;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #8b949e;
  font-size: 14px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-state .el-icon {
  font-size: 20px;
  margin-bottom: 8px;
}
</style>
