<script setup>
import { computed } from 'vue'
import loadingIcon from '@/assets/icons/loading.svg'
import completedIcon from '@/assets/icons/upload-done.svg'

const props = defineProps({
  fileName: {
    type: String,
    default: '20250701XX法院诉讼文件.pdf',
  },
  fileSize: {
    type: String,
    default: '3.2 MB',
  },
  progress: {
    type: Number,
    default: 50,
  },
  status: {
    type: String,
    default: 'uploading', // 'uploading', 'creating', 'submitting', 'completed', 'error'
  },
  error: {
    type: String,
    default: '',
  },
  taskId: {
    type: [Number, String],
    default: null,
  },
  resultFileUrl: {
    type: String,
    default: '',
  },
})

const isUploading = computed(() => props.status === 'uploading')
const isCreating = computed(() => props.status === 'creating')
const isSubmitting = computed(() => props.status === 'submitting')
const isCompleted = computed(() => props.status === 'completed')
const isError = computed(() => props.status === 'error')
const isPending = computed(() => props.status === 'pending')
const isReviewing = computed(() => props.status === 'reviewing')
const isAnalyzing = computed(() => props.status === 'analyzing')
const isRejected = computed(() => props.status === 'rejected')

const progressStyle = computed(() => ({
  width: `${props.progress}%`,
  backgroundColor:
    isError.value || isRejected.value ? '#dc3545' : isCompleted.value ? '#28a745' : '#0057d9',
}))

const statusText = computed(() => {
  if (isUploading.value) return '正在上传文件...'
  if (isCreating.value) return '正在创建任务...'
  if (isSubmitting.value) return '正在提交任务...'
  if (isPending.value) return '待提交'
  if (isReviewing.value) return '待审核'
  if (isAnalyzing.value) return '分析中...'
  if (isCompleted.value) return '检测完成'
  if (isRejected.value) return '已驳回'
  if (isError.value) return props.error || '上传失败'
  return ''
})

const statusIcon = computed(() => {
  if (isUploading.value || isCreating.value || isSubmitting.value || isAnalyzing.value)
    return loadingIcon
  if (isCompleted.value) return completedIcon
  return ''
})

const showDownloadButton = computed(() => {
  return isCompleted.value && props.resultFileUrl
})

const downloadResult = () => {
  if (props.resultFileUrl) {
    window.open(props.resultFileUrl, '_blank')
  }
}

const showRetryButton = computed(() => {
  return isError.value
})

const retryUpload = () => {
  // 这里可以触发重新上传的逻辑
  console.log('重新上传文件')
}
</script>

<template>
  <div class="upload-item">
    <div class="main-content">
      <div class="content-wrapper">
        <div class="top-row">
          <div class="file-info">
            <div class="file-icon">
              <img src="@/assets/icons/pdf.svg" alt="PDF Icon" />
            </div>
            <div class="file-details">
              <span class="file-name">{{ fileName }}</span>
              <span class="file-size">{{ fileSize }}</span>
            </div>
          </div>
        </div>
        <div class="bottom-row">
          <div class="progress-bar-container">
            <div class="progress-bar" :style="progressStyle"></div>
          </div>
          <div class="status-details">
            <span class="progress-percentage">{{ progress }}%</span>
            <div class="upload-status">
              <img
                v-if="statusIcon"
                :src="statusIcon"
                :class="{ 'icon-loading': isUploading || isCreating || isSubmitting }"
                alt="Status Icon"
              />
              <span :class="{ 'error-text': isError }">{{ statusText }}</span>
            </div>
            <button v-if="showRetryButton" class="btn btn-primary" @click="retryUpload">
              重试
            </button>
            <button v-if="showDownloadButton" class="btn btn-success" @click="downloadResult">
              下载报告
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.upload-item {
  border-radius: 8px;
  background-color: #f8f9fa;
  padding: 16px;
}

.main-content {
  display: flex;
  align-items: center;
}

.content-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.top-row,
.bottom-row {
  display: flex;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 8px;
}

.file-icon img {
  width: 28px;
  height: 28px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
}

.file-size {
  font-size: 12px;
  color: #7f8792;
}

.bottom-row {
  gap: 16px;
}

.progress-bar-container {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  width: 80%;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition:
    width 0.3s ease,
    background-color 0.3s ease;
}

.status-details {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #7f8792;
}

.progress-percentage {
  font-weight: 500;
  display: inline-block;
  width: 36px;
  flex-shrink: 0;
  color: #222529;
}

.upload-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 114px;
  gap: 4px;
}

.upload-status img {
  width: 14px;
  height: 14px;
}

.btn-danger {
  background-color: transparent;
  color: #f5222d;
  border-color: transparent;
  padding: 4px 8px;
  font-size: 12px;
}

.btn-danger:hover {
  background-color: #fff1f0;
  border-color: #ffa39e;
}

.separator {
  width: 1px;
  height: 60px;
  background-color: #e9ecef;
  margin: 0 24px;
}

.action-area {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button {
  width: 80px;
  height: 32px;
  border-radius: 217px;
  border: 1px solid #0057d9;
  background-color: #fff;
  color: #0057d9;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-button:disabled {
  border-color: #bfccdb;
  color: #bfccdb;
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.action-button:not(:disabled):hover {
  background-color: #0057d9;
  color: #fff;
}

.error-text {
  color: #dc3545;
}

.btn {
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  border-color: #0057d9;
  background-color: #0057d9;
  color: #fff;
}

.btn-primary:hover {
  background-color: #004bb5;
  border-color: #004bb5;
}

.btn-success {
  border-color: #28a745;
  background-color: #28a745;
  color: #fff;
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}
</style>
