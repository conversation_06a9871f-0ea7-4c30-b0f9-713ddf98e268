<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const mobile = ref('15088889999')
const password = ref('123456')
const agreeToTerms = ref(false)
const loading = ref(false)

async function handleSubmit() {
  // 检查是否同意协议
  if (!agreeToTerms.value) {
    ElMessage.warning('请先阅读并同意用户服务协议和隐私政策')
    return
  }

  loading.value = true
  try {
    // 使用 userStore 的登录方法
    await userStore.userLogin({
      mobile: mobile.value,
      password: password.value,
    })

    ElMessage.success('登录成功')

    // 获取重定向路径
    const redirect = route.query.redirect || '/'
    await router.push(redirect)
  } catch (error) {
    console.error('Login failed:', error)
    ElMessage.error(error.message || '登录请求失败，请稍后再试')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-box">
        <div class="logo-section">
          <img src="@/assets/images/logo.png" alt="Logo" class="logo-img" />
          <span class="logo-text">法律大联盟</span>
        </div>

        <form @submit.prevent="handleSubmit" class="login-form">
          <div class="form-group">
            <label for="mobile">手机号</label>
            <input type="text" id="mobile" v-model="mobile" placeholder="请输入手机号" />
          </div>
          <div class="form-group">
            <div class="label-group">
              <label for="password">密码</label>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>
            <input type="password" id="password" v-model="password" placeholder="请输入密码" />
          </div>

          <div class="agreement">
            <input type="checkbox" id="agreement-check" v-model="agreeToTerms" />
            <label for="agreement-check">
              我已阅读并同意
              <a href="#">《用户服务协议》</a>
              <a href="#">《隐私政策》</a>
            </label>
          </div>

          <button type="submit" class="login-btn" :disabled="!agreeToTerms || loading">
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: url('@/assets/images/image.png') center / cover no-repeat;
}

.login-container {
  width: 400px;
  padding: 40px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 20px rgba(0, 26, 64, 0.15);
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.logo-img {
  width: 40px;
  height: 40px;
  margin-right: 16px;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #222529;
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: #595e66;
  margin-bottom: 8px;
}

.label-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  font-size: 14px;
  color: #0057d9;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.form-group input[type='text'],
.form-group input[type='password'] {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid #e1e8f0;
  border-radius: 4px;
  font-size: 16px;
  background-color: #fff;
  transition: border-color 0.3s;
}

.form-group input[type='text']:focus,
.form-group input[type='password']:focus {
  outline: none;
  border-color: #0057d9;
}

.login-btn {
  width: 100%;
  height: 40px;
  border-radius: 6px;
  background-color: #0057d9;
  color: #ffffff;
  font-size: 16px;
  border: none;
  cursor: pointer;
  margin-top: 12px;
  transition: background-color 0.3s;
}

.login-btn:hover:not(:disabled) {
  background-color: #004ab6;
}

.login-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.agreement {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #727e91;
  white-space: nowrap;
}

.agreement input[type='checkbox'] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.agreement a {
  color: #0057d9;
  text-decoration: none;
}

.agreement a:hover {
  text-decoration: underline;
}
</style>
