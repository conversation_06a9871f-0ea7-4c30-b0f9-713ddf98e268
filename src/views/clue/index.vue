<script setup>
import { ref } from 'vue'
import DownloadDialog from '@/components/DownloadDialog.vue'
import InsufficientBalanceDialog from '@/components/InsufficientBalanceDialog.vue'

const files = ref([
  { id: 1, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 2, name: '20250701XX法院诉讼文件.doc', size: '3.2 MB', type: 'word' },
  { id: 3, name: '20250701XX法院诉讼文件.txt', size: '3.2 MB', type: 'text' },
  { id: 4, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 5, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 6, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 7, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 8, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 9, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 10, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 11, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 12, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 13, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 14, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 15, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  {
    id: 16,
    name: '20250701XX法院诉讼文件.pdf',
    size: '3.2 MB',
    type: 'pdf',
    hasInsufficientBalance: true,
  },
  { id: 17, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 18, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 19, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 20, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
  { id: 21, name: '20250701XX法院诉讼文件.pdf', size: '3.2 MB', type: 'pdf' },
])

const pagination = ref({
  totalItems: 101,
  itemsPerPage: 10,
  currentPage: 1,
  totalPages: 11,
})

// 下载对话框状态
const showDownloadDialog = ref(false)
const selectedFile = ref(null)
const remainingDownloads = ref(7)

// 余额不足对话框状态
const showInsufficientBalanceDialog = ref(false)
const selectedInsufficientFile = ref(null)

// 处理预览功能
const handlePreview = (file) => {
  console.log('预览文件:', file.name)
  // 这里可以添加预览逻辑
}

// 处理下载功能
const handleDownload = (file) => {
  selectedFile.value = file
  showDownloadDialog.value = true
}

// 处理余额不足提醒
const handleInsufficientBalance = (file) => {
  selectedInsufficientFile.value = file
  showInsufficientBalanceDialog.value = true
}

// 关闭下载对话框
const handleCloseDownloadDialog = () => {
  showDownloadDialog.value = false
  selectedFile.value = null
}

// 确认下载
const handleConfirmDownload = (file) => {
  console.log('确认下载文件:', file.name)
  // 这里执行实际的下载逻辑
  remainingDownloads.value -= 1
  handleCloseDownloadDialog()
}

// 关闭余额不足对话框
const handleCloseInsufficientBalanceDialog = () => {
  showInsufficientBalanceDialog.value = false
  selectedInsufficientFile.value = null
}

// 处理购买
const handlePurchase = () => {
  console.log('跳转到购买页面')
  // 这里可以跳转到购买页面或执行购买逻辑
  handleCloseInsufficientBalanceDialog()
}
</script>

<script>
export default {
  name: 'clue',
}
</script>
<template>
  <div class="clue-container">
    <h1 class="title">公益诉讼线索</h1>

    <div class="file-grid scroll-container">
      <div v-for="file in files" :key="file.id" class="file-card">
        <div class="file-card__content">
          <div class="file-card__icon-wrapper">
            <img
              v-if="file.type === 'pdf'"
              src="@/assets/icons/pdf.svg"
              alt="PDF icon"
              class="file-card__icon-img"
            />
            <div v-else-if="file.type === 'word'" class="file-card__icon-placeholder is-word">
              W
            </div>
            <div v-else-if="file.type === 'text'" class="file-card__icon-placeholder is-text">
              T
            </div>
          </div>
          <div class="file-card__info">
            <p class="file-card__name">{{ file.name }}</p>
            <p class="file-card__size">{{ file.size }}</p>
          </div>
        </div>

        <!-- 正常文件的操作按钮 -->
        <div v-if="!file.hasInsufficientBalance" class="file-card__actions">
          <button class="btn" @click="handlePreview(file)">预览</button>
          <button class="btn" @click="handleDownload(file)">下载</button>
        </div>

        <!-- 余额不足的提醒 -->
        <div v-if="file.hasInsufficientBalance" class="file-card__error">
          <button class="btn" @click="handleInsufficientBalance(file)">余额不足提醒</button>
        </div>
      </div>
    </div>

    <div class="pagination">
      <span class="pagination__total">共 {{ pagination.totalItems }} 项数据</span>
      <div class="pagination__per-page">
        <span>{{ pagination.itemsPerPage }}条/页</span>
      </div>
      <nav class="pagination__nav">
        <button class="pagination__btn is-active">1</button>
        <button class="pagination__btn">2</button>
        <button class="pagination__btn">3</button>
        <button class="pagination__btn">4</button>
        <button class="pagination__btn">5</button>
        <span class="pagination__ellipsis">...</span>
        <button class="pagination__btn">{{ pagination.totalPages }}</button>
      </nav>
    </div>

    <!-- 下载对话框 -->
    <DownloadDialog
      :visible="showDownloadDialog"
      :file="selectedFile || {}"
      :remaining-downloads="remainingDownloads"
      @close="handleCloseDownloadDialog"
      @confirm="handleConfirmDownload"
    />

    <!-- 余额不足对话框 -->
    <InsufficientBalanceDialog
      :visible="showInsufficientBalanceDialog"
      :file="selectedInsufficientFile || {}"
      :remaining-downloads="remainingDownloads"
      @close="handleCloseInsufficientBalanceDialog"
      @purchase="handlePurchase"
    />
  </div>
</template>

<style scoped>
.clue-container {
  --primary-color: #0057d9;
  --text-color-primary: #222529;
  --text-color-secondary: #7f8792;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --surface-hover-color: #ecf4ff;
  --border-color: #ebebeb;
  --error-color: #f84d4e;

  width: 100%;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 20px;
  flex-shrink: 0; /* 防止标题被压缩 */
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  align-content: start;
  /* 滚动样式由全局 .scroll-container 类提供 */
}

.file-card {
  height: 80px;
  background-color: var(--surface-color);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s;
  position: relative;
}

.file-card:hover {
  background-color: var(--surface-hover-color);
}

.file-card:hover .file-card__content {
  opacity: 0.3;
}

.file-card:hover .file-card__actions,
.file-card:hover .file-card__error {
  opacity: 1;
  visibility: visible;
}

.file-card__content {
  display: flex;
  align-items: center;
  gap: 16px;
  transition:
    opacity 0.3s,
    visibility 0.3s;
}

.file-card__icon-wrapper {
  width: 48px;
  height: 48px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-card__icon-img {
  width: 26px;
  height: 30px;
}

.file-card__icon-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 700;
}
.file-card__icon-placeholder.is-word {
  background-color: #2a5699;
}
.file-card__icon-placeholder.is-text {
  background-color: #4caf50;
}

.file-card__info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-card__name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0;
}

.file-card__size {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin: 0;
}

.file-card__actions {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  display: flex;
  gap: 8px;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
}

.btn {
  border-radius: 217px;
  border: 1px solid var(--primary-color);
  background-color: var(--background-color);
  color: var(--primary-color);
  font-size: 14px;
  padding: 7px 16px;
  cursor: pointer;
  white-space: nowrap;
}

.file-card__error {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
}

.file-card__error .btn {
  margin-bottom: 4px;
}

.error-caption {
  font-size: 12px;
  color: var(--error-color);
  margin: 0;
  position: absolute;
  bottom: -18px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  text-align: center;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 24px;
  font-size: 14px;
  color: var(--text-color-primary);
  flex-shrink: 0; /* 防止分页区域被压缩 */
}

.pagination__per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 8px 12px;
}

.pagination__nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination__btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #dcdcdc;
  background-color: var(--background-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.pagination__btn.is-nav {
  border: none;
  background-color: transparent;
}

.pagination__btn.is-active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination__ellipsis {
  margin: 0 4px;
}
</style>
