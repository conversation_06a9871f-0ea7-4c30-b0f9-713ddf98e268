<script setup>
import { computed } from 'vue'
import pdfIconSrc from '@/assets/icons/pdf.svg'
import wordIconSrc from '@/assets/icons/word.svg'
import defaultFileIconSrc from '@/assets/icons/default-file.svg'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: Object,
    default: () => ({
      name: '',
      size: '',
      type: 'pdf',
    }),
  },
  remainingDownloads: {
    type: Number,
    default: 7,
  },
})

// 定义事件
const emit = defineEmits(['close', 'confirm'])

// 计算属性
const fileIcon = computed(() => {
  switch (props.file.type) {
    case 'pdf':
      return pdfIconSrc
    case 'word':
      return wordIconSrc
    default:
      return defaultFileIconSrc
  }
})

// 事件处理
const handleClose = () => {
  emit('close')
}

const handleConfirm = () => {
  emit('confirm', props.file)
}
</script>

<template>
  <div v-if="visible" class="dialog-overlay" @click="handleClose">
    <div class="dialog" @click.stop>
      <!-- 标题栏 -->
      <div class="dialog__header">
        <h3 class="dialog__title">下载提示</h3>
        <button class="dialog__close-btn" @click="handleClose">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              d="M12 4L4 12M4 4L12 12"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="dialog__content">
        <!-- 提示信息 -->
        <div class="dialog__message">
          <div class="message-icon">
            <img src="@/assets/icons/info.svg" alt="info icon" />
          </div>
          <span class="message-text">是否下载当前文件？</span>
        </div>

        <!-- 下载次数提示 -->
        <div class="download-info">
          <span class="download-info__label">下载次数：</span>
          <span class="download-info__text">
            消耗 <span class="cost-number">1</span> 次（还可以使用
            <span class="remaining-number">{{ remainingDownloads }}</span> 次）
          </span>
        </div>

        <!-- 文件信息 -->
        <div class="file-preview">
          <div class="file-preview__icon">
            <img :src="fileIcon" :alt="`${file.type} icon`" />
          </div>
          <div class="file-preview__info">
            <p class="file-preview__name">{{ file.name }}</p>
            <p class="file-preview__size">{{ file.size }}</p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog__actions">
        <button class="btn btn--secondary" @click="handleClose">取消</button>
        <button class="btn btn--primary" @click="handleConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  width: 512px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
}

.dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #222529;
  margin: 0;
}

.dialog__close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #7f8792;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}

.dialog__close-btn:hover {
  background-color: #f0f1f2;
  color: #222529;
  transform: scale(1.1) rotate(90deg);
}

.dialog__close-btn:active {
  transform: scale(1) rotate(90deg);
  background-color: #e5e7eb;
}

.dialog__content {
  padding: 20px 24px;
}

.dialog__message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 16px;
}

.message-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.message-text {
  font-size: 16px;
  font-weight: 500;
  color: #222529;
  line-height: 1.5;
}

.download-info {
  margin-left: 28px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #7f8792;
}

.download-info__label {
  margin-right: 8px;
}

.cost-number {
  font-weight: 500;
  color: #f5222d;
}

.remaining-number {
  font-weight: 500;
  color: #222529;
}

.file-preview {
  margin-left: 28px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-preview__icon {
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-preview__icon img {
  width: 26px;
  height: 30px;
}

.file-preview__info {
  flex: 1;
}

.file-preview__name {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
  margin: 0 0 8px;
  word-break: break-all;
}

.file-preview__size {
  font-size: 14px;
  color: #7f8792;
  margin: 0;
}

.dialog__actions {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition:
    width 0.3s ease,
    height 0.3s ease;
}

.btn:active::before {
  width: 200px;
  height: 200px;
}

.btn--secondary {
  background-color: #ffffff;
  color: #222529;
  border: 1px solid #dcdfe6;
}

.btn--secondary:hover {
  background-color: #f8f9fa;
  color: #0057d9;
  border-color: #0057d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn--secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn--primary {
  background-color: #0057d9;
  color: #ffffff;
  border: 1px solid #0057d9;
  box-shadow: 0 2px 8px rgba(0, 87, 217, 0.25);
}

.btn--primary:hover {
  background-color: #004cb8;
  border-color: #004cb8;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 87, 217, 0.3);
}

.btn--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 87, 217, 0.25);
}
</style>
