<script setup>
// 定义组件属性
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['purchase', 'logout', 'close'])

// 处理菜单项点击
const handlePurchase = () => {
  emit('purchase')
  emit('close')
}

const handleLogout = () => {
  emit('logout')
  emit('close')
}
</script>

<template>
  <transition name="dropdown">
    <div v-if="visible" class="dropdown-menu" @click.stop>
      <div class="menu-list">
        <!-- 购买套餐菜单项 -->
        <div class="menu-item" @click="handlePurchase">
          <div class="menu-icon">
            <img src="@/assets/icons/shop.svg" alt="购买套餐" class="icon-image" />
          </div>
          <span class="menu-text">购买套餐/次数</span>
        </div>

        <!-- 退出菜单项 -->
        <div class="menu-item" @click="handleLogout">
          <div class="menu-icon">
            <img src="@/assets/icons/logout.svg" alt="退出" class="icon-image" />
          </div>
          <span class="menu-text">退出</span>
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped>
.dropdown-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 8px;
  z-index: 1000;
  min-width: 160px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transform-origin: bottom right;
}

.dropdown-enter-active,
.dropdown-leave-active {
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.menu-list {
  padding: 4px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  font-family:
    'HarmonyOS Sans',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
  color: #374151;
}

.menu-item:hover {
  background-color: #f3f4f6;
}

.menu-item:active {
  background-color: #e5e7eb;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.icon-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.menu-text {
  flex: 1;
  white-space: nowrap;
  font-weight: 400;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 140px;
  }

  .menu-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .menu-icon {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }
}
</style>
