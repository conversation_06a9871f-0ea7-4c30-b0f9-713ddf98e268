<script setup>
import { computed, ref } from 'vue'
import pdfIconSrc from '@/assets/icons/pdf.svg'
import wordIconSrc from '@/assets/icons/word.svg'
import defaultFileIconSrc from '@/assets/icons/default-file.svg'
import PurchaseModal from './PurchaseModal.vue'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: Object,
    default: () => ({
      name: '',
      size: '',
      type: 'pdf',
    }),
  },
  remainingDownloads: {
    type: Number,
    default: 0,
  },
  userInfo: {
    type: Object,
    default: () => ({
      name: '用户',
      avatar: '',
      remainingCount: 0,
    }),
  },
})

// 定义事件
const emit = defineEmits(['close', 'purchase'])

// 显示状态：'insufficient' | 'purchase'
const currentView = ref('insufficient')

// 计算属性
const fileIcon = computed(() => {
  switch (props.file.type) {
    case 'pdf':
      return pdfIconSrc
    case 'word':
      return wordIconSrc
    default:
      return defaultFileIconSrc
  }
})

// 事件处理
const handleClose = () => {
  currentView.value = 'insufficient' // 重置视图
  emit('close')
}

const handleShowPurchase = () => {
  currentView.value = 'purchase'
}

// 处理购买（从 PurchaseModal 传递过来的事件）
const handlePurchase = (purchaseData) => {
  emit('purchase', purchaseData)
}
</script>

<template>
  <div v-if="visible" class="dialog-overlay" @click="handleClose">
    <!-- 余额不足提示界面 -->
    <div v-if="currentView === 'insufficient'" class="dialog" @click.stop>
      <!-- 标题栏 -->
      <div class="dialog__header">
        <h3 class="dialog__title">余额不足提示</h3>
        <button class="dialog__close-btn" @click="handleClose">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              d="M12 4L4 12M4 4L12 12"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="dialog__content">
        <!-- 提示信息 -->
        <div class="dialog__message">
          <div class="message-icon">
            <img src="@/assets/icons/info.svg" alt="info icon" />
          </div>
          <span class="message-text">当前可下载次数不足，请购买</span>
        </div>

        <!-- 下载次数提示 -->
        <div class="download-info">
          <span class="download-info__label">下载次数：</span>
          <span class="download-info__text">
            消耗 <span class="cost-number">1</span> 次（还可以使用
            <span class="remaining-number">{{ remainingDownloads }}</span> 次）
          </span>
        </div>

        <!-- 文件信息 -->
        <div class="file-preview">
          <div class="file-preview__icon">
            <img :src="fileIcon" :alt="`${file.type} icon`" />
          </div>
          <div class="file-preview__info">
            <p class="file-preview__name">{{ file.name }}</p>
            <p class="file-preview__size">{{ file.size }}</p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog__actions">
        <button class="btn btn--primary" @click="handleShowPurchase">立即购买</button>
        <button class="btn btn--secondary" @click="handleClose">确定</button>
      </div>
    </div>

    <!-- 购买套餐界面 -->
    <PurchaseModal
      v-else-if="currentView === 'purchase'"
      :visible="true"
      :user-info="userInfo"
      @close="handleClose"
      @purchase="handlePurchase"
    />
  </div>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  width: 512px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
}

.dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #222529;
  margin: 0;
}

.dialog__close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #7f8792;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.dialog__close-btn:hover {
  background-color: #f5f5f5;
  color: #222529;
  transform: scale(1.05);
}

.dialog__close-btn:active {
  transform: scale(0.95);
}

.dialog__content {
  padding: 20px 24px;
}

.dialog__message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 16px;
}

.message-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.message-text {
  font-size: 16px;
  font-weight: 500;
  color: #222529;
  line-height: 1.5;
}

.download-info {
  margin-left: 28px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #7f8792;
}

.download-info__label {
  margin-right: 8px;
}

.cost-number {
  font-weight: 500;
  color: #f5222d;
}

.remaining-number {
  font-weight: 500;
  color: #222529;
}

.file-preview {
  margin-left: 28px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-preview__icon {
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-preview__icon img {
  width: 26px;
  height: 30px;
}

.file-preview__info {
  flex: 1;
}

.file-preview__name {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
  margin: 0 0 8px;
  word-break: break-all;
}

.file-preview__size {
  font-size: 14px;
  color: #7f8792;
  margin: 0;
}

.dialog__actions {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Button styles are now handled globally by main.css */
</style>
