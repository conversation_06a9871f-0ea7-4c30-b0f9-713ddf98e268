<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import UserDropdownMenu from './UserDropdownMenu.vue'

const userStore = useUserStore()

// 响应式状态
const isMenuVisible = ref(false)
const profileRef = ref(null)

// 从 store 获取用户信息
const user = computed(() => ({
  name: userStore.userInfo.nickname || userStore.userInfo.username || '用户',
  avatar: userStore.userInfo.avatar || '',
}))

// 计算是否显示占位符
const showFallbackAvatar = computed(() => !user.value.avatar)
const avatarInitial = computed(() => user.value.name.charAt(0))

// 切换菜单显示状态
const toggleMenu = () => {
  isMenuVisible.value = !isMenuVisible.value
}

// 关闭菜单
const closeMenu = () => {
  isMenuVisible.value = false
}

// 处理购买套餐
const handlePurchase = () => {
  console.log('跳转到购买套餐页面')
  // 这里可以添加路由跳转或打开购买对话框的逻辑
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await userStore.logout()
    closeMenu()
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (profileRef.value && !profileRef.value.contains(event.target)) {
    closeMenu()
  }
}

// 生命周期钩子
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)

  // 获取用户信息
  if (userStore.isLoggedIn && !userStore.userInfo.id) {
    try {
      await userStore.fetchUserProfile()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="profileRef" class="user-profile">
    <!-- 用户头像/触发按钮 -->
    <button class="profile-trigger" @click="toggleMenu" :class="{ active: isMenuVisible }">
      <div class="avatar">
        <img v-if="!showFallbackAvatar" :src="user.avatar" alt="用户头像" class="avatar-image" />
        <div v-else class="avatar-fallback">
          {{ avatarInitial }}
        </div>
      </div>
      <div class="dropdown-arrow">
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="currentColor"
          :class="{ rotated: isMenuVisible }"
        >
          <path d="M6 8L2 4h8L6 8z" />
        </svg>
      </div>
    </button>

    <!-- 下拉菜单 -->
    <UserDropdownMenu
      :visible="isMenuVisible"
      @purchase="handlePurchase"
      @logout="handleLogout"
      @close="closeMenu"
    />
  </div>
</template>

<style scoped>
.user-profile {
  position: relative;
  display: inline-block;
}

.profile-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.profile-trigger:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.profile-trigger.active {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background-color: #f66826;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border-radius: 50%;
}

.dropdown-arrow {
  display: flex;
  align-items: center;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.dropdown-arrow svg.rotated {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-trigger {
    padding: 6px 8px;
    gap: 6px;
  }

  .avatar {
    width: 28px;
    height: 28px;
  }
}
</style>
