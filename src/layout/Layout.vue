<template>
  <div class="app-layout">
    <Sidebar />
    <div class="main-container">
      <AppMain />
    </div>
  </div>
</template>

<script>
import Sidebar from './components/Sidebar.vue'
import AppMain from './components/AppMain.vue'

export default {
  name: 'MainLayout',
  components: {
    Sidebar,
    AppMain,
  },
}
</script>

<style scoped>
/* Styles from original src/components/layout/AppLayout.vue to keep UI the same */
.app-layout {
  display: flex;
  background-color: #f3f5f6;
  height: 100vh;
  padding: 8px 12px;
  overflow: hidden;
}

.main-container {
  flex-grow: 1;
  margin-left: 12px;
  display: flex;
  overflow: hidden;
}
</style>
