<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <aside class="sidebar">
    <Logo />
    <SidebarMenu />
    <UserInfoContainer />
  </aside>
</template>

<script setup>
import Logo from './Logo.vue'
import SidebarMenu from './SidebarMenu.vue'
import UserInfoContainer from './UserInfoContainer.vue'
</script>

<style scoped>
.sidebar {
  width: 224px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 16px);
  flex-shrink: 0;
}
</style>
