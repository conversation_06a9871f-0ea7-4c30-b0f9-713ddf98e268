<template>
  <section class="app-main">
    <router-view v-slot="{ Component }">
      <keep-alive :include="cachedViews">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </section>
</template>

<script setup>
import { computed } from 'vue'
import { useCachedViewsStore } from '@/stores/cachedViews.js'

const cachedViewsStore = useCachedViewsStore()
const cachedViews = computed(() => cachedViewsStore.cachedViews)
</script>

<style scoped>
.app-main {
  flex-grow: 1;
  border-radius: 12px;
  background-color: #ffffff;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
