<template>
  <router-view />
</template>

<script setup>
import { watch } from 'vue';
import { useRoute } from 'vue-router';
import { useCachedViewsStore } from '@/stores/cachedViews.js';

const route = useRoute();
const cachedViewsStore = useCachedViewsStore();

watch(
  () => route.name,
  () => {
    if (route.name && route.meta.cache) {
      cachedViewsStore.addCachedView(route);
    }
  },
  { immediate: true }
);
</script>

<style>
/* Global styles can be placed here */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}
</style>
