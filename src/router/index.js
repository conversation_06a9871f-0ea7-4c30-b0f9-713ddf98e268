import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import Layout from '@/layout/Layout.vue'
import LoginView from '../views/login/index.vue'
import FileCheckView from '../views/file-check/index.vue'
import ClueView from '../views/clue/index.vue'
import MyInfoView from '../views/my-info/index.vue'
import NotFound from '../views/error-page/404.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/',
      component: Layout,
      redirect: '/file-check?type=0',
      children: [
        {
          path: 'file-check',
          name: 'file-check',
          component: FileCheckView,
          meta: {
            title: '文书检查',
            icon: 'menu-file-check.svg',
            cache: true,
            submenu: [
              { title: '判决书', type: '0' },
              { title: '处罚决定书', type: '1' },
              { title: '租赁权拍卖公告', type: '2' },
              { title: '合同', type: '3' },
            ],
          },
        },
        {
          path: 'clue',
          name: 'clue',
          component: ClueView,
          meta: {
            title: '公益诉讼线索',
            icon: 'menu-clue.svg',
            cache: true,
          },
        },
        {
          path: 'my-info',
          name: 'my-info',
          component: MyInfoView,
          meta: {
            title: '我的信息',
            icon: 'menu-my.svg',
            cache: true,
          },
        },
      ],
    },
  ],
})

// 404 page must be placed at the end !!!
router.addRoute({
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: NotFound,
  meta: { hidden: true },
})

// 白名单路由（不需要登录验证的路由）
const whiteList = ['/login', '/404']

// 路由前置守卫
router.beforeEach((to, _from, next) => {
  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn
  const isLoginPage = to.path === '/login'
  const isWhitelisted = whiteList.includes(to.path)

  if (isLoggedIn && isLoginPage) {
    next({ path: '/' })
  } else if (isLoggedIn || isWhitelisted) {
    next()
  } else {
    ElMessage.warning('请先登录')
    next(`/login?redirect=${to.path}`)
  }
})

const getPageTitle = (pageTitle) => {
  const title = '法律大联盟'
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return title
}

router.afterEach((to) => {
  document.title = getPageTitle(to.meta.title)
})

export default router
