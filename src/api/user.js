import request from '@/utils/request'

// ==================== 用户认证相关接口 ====================

/**
 * 使用手机号和密码登录
 * @param {object} data - 包含 mobile 和 password 的对象
 * @returns {Promise}
 */
export function login(data) {
  return request.post('/app-api/member/auth/login', data)
}

// ==================== 用户个人中心相关接口 ====================

/**
 * 获得基本信息
 * @returns {Promise}
 */
export function getUserProfile() {
  return request.get('/app-api/member/user/get')
}

/**
 * 修改基本信息
 * @param {object} data - 用户信息
 * @param {string} data.nickname - 用户昵称
 * @param {string} data.avatar - 头像
 * @param {number} data.sex - 性别（1-男，2-女）
 * @returns {Promise}
 */
export function updateUserProfile(data) {
  return request.put('/app-api/member/user/update', data)
}

/**
 * 修改用户密码
 * @param {object} data - 密码信息
 * @param {string} data.password - 新密码
 * @param {string} data.code - 手机验证码
 * @returns {Promise}
 */
export function updateUserPassword(data) {
  return request.put('/app-api/member/user/update-password', data)
}

/**
 * 修改用户手机
 * @param {object} data - 手机信息
 * @param {string} data.code - 新手机验证码
 * @param {string} data.mobile - 手机号
 * @param {string} data.oldCode - 原手机验证码（可选）
 * @returns {Promise}
 */
export function updateUserMobile(data) {
  return request.put('/app-api/member/user/update-mobile', data)
}

/**
 * 重置密码
 * @param {object} data - 重置密码信息
 * @param {string} data.password - 新密码
 * @param {string} data.code - 手机验证码
 * @param {string} data.mobile - 手机号
 * @returns {Promise}
 */
export function resetUserPassword(data) {
  return request.put('/app-api/member/user/reset-password', data)
}
