@import './base.css';

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

/* Removed empty hover rule for cleaner code */

/* 公共滚动样式 */
.scroll-container {
  overflow-y: auto;
  flex-grow: 1;
  min-height: 0;
}

/* 美化滚动条样式 */
.scroll-container::-webkit-scrollbar {
  width: 6px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Firefox 滚动条样式 */
.scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f8f9fa;
}

.fixed-header {
  flex-shrink: 0;
}

/* General <PERSON><PERSON> Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  border: 1px solid transparent;
  user-select: none;
  white-space: nowrap;
}

.btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: #0057d9;
  color: #ffffff;
  border-color: #0057d9;
}

.btn-primary:hover {
  background-color: #004cb8;
  border-color: #004cb8;
}

.btn-secondary {
  background-color: #f0f1f2;
  color: #222529;
  border-color: #dcdfe6;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
  border-color: #c0c4cc;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.icon-loading {
  animation: spin 1s linear infinite;
}
